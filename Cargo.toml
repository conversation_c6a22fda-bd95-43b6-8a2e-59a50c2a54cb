[package]
name = "port-checker"
version = "0.1.0"
edition = "2021"
authors = ["<PERSON>"]
license = "MIT"
description = "A fast and user-friendly command-line tool to check port usage"
homepage = "https://github.com/william-xue/port-checker"
repository = "https://github.com/william-xue/port-checker"
readme = "README.md"
keywords = ["port", "network", "cli", "system", "tools"]
categories = ["command-line-utilities", "network-programming"]

[[bin]]
name = "port-checker"
path = "src/main.rs"

[dependencies]
clap = { version = "4.0", features = ["derive"] }
anyhow = "1.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
colored = "2.0"
tabled = "0.14"

# 平台相关依赖
[target.'cfg(unix)'.dependencies]
nix = "0.27"

[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winsock2", "ws2ipdef", "iphlpapi", "tcpmib"] }
